<?php

return [

    'my_attendance' => 'My Attendance',
    'attendance' => 'Attendance',
    'machines' => 'Attendance Machines',
    'machineList' => 'Machin List',
    'machinemanagement' => 'Machine Management',
    'ip' => 'IP',
    'location' => 'Location',
    'name' => 'Name',
    'active' => 'Active',
    'deactive' => 'Deactive',
    "checkmachines" => 'Check Machines',
    'skip_chech' => 'Skip Check',
    'branch' => 'Branch',
    'deactive_machines' => 'Deactive Machines',
    're_store_img' => 'Store Agian',
    're_store_info' => 'The previous data of the selected machines will be deleted and re-stored',
    'machine_allready_inserted' => 'The following machines have already been uploaded',
    'uploads_att_progress' => 'upload is in progress',
    'machine_has_no_data' => 'The selected machines do not have data on this date',
    'emp_attendance' => 'Employee Attendance',
    'check_images' => 'Check Images',
    'leave_mgmt' => 'Leave Management',
    'station' => 'Station',
    'stations' => 'Stations',
    'noOfMachines' => 'Count of Machines',
    'stationLimitInofText' => 'Each station can hold from one to 24 active attendance machines',
    'macAddress' =>'Mac Address',
    'stationLimitInofText' => 'Each station can hold from one to 24 attendance machines',
    'macAddress' => 'Mac Address',
    'setAttStationsPermissions' => 'Attendance Stations Permissions',
    'stationAccess' => 'Access to Attendance Stations',
    'denyDepAccess' => 'Deny Department Access',
    'cant_store_duplicate_machine_mac_at_same_branch' => 'It is not possible to register a machine with a duplicate mac in a single branch',
    'AccessToAllDepartments' => 'َAccess to All Departments',
    'leaves_export_to_excel_file_title' => 'راپور رخصتی های :employee (:date)',
    'deactive_machines_found' => 'Deactivated machines found',
    'leave_request_record_deleted'=>'Your leave request has been successfully deleted',
    'leave_request_record_delete_failed'=>'Deleting your leave request failed.',
    'student_export_to_excel_file_title' => '(:date) Students Report',
    'one_day_leave_only' => 'You can only request leave for one day. Please adjust the leave dates accordingly',
    'request_sick_leave_is_only_5_days' => 'You can only request sick leave for 5 days',
    'leave_request_valid_date' => 'leave request valid date',
    'taken_leaves_days_no' => 'Taken leaves days no',
    'add_taken_leaves' => 'add previous organization taken leave',
    'request_date_is_more_than_50_days' => 'requested days are more than 50 days',















];
