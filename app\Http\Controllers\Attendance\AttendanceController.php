<?php

namespace App\Http\Controllers\Attendance;

use App\Http\Controllers\Controller;
use App\Http\Requests\Attendance\ImageRejectionRequest;
use App\Models\Attendance\AttendanceNotifyFire;
use App\Models\Recruitment\EmployeeStatus;
use App\Repositories\Attendance\Attendance\AttendanceRepository;
use App\Repositories\Attendance\Holiday\HolidayRepository;
use App\Repositories\Attendance\Leave\LeaveRepository;
use App\Repositories\MasterData\EmployeeMawqifRepository;
use App\Repositories\MasterData\LeaveTypeRepository;
use App\Repositories\Recruitment\EmployeeRepository;
use App\Repositories\Recruitment\EmployeeStatusPeriodRepository;
use App\Repositories\Tashkilat\Department\DepartmentRepository;
use App\Traits\ResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB as FacadesDB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\View;
use PhpOffice\PhpSpreadsheet\Calculation\DateTimeExcel\Month;

class AttendanceController extends Controller
{
    use ResponseTrait;

    private $attendanceRepository;
    private $departmentRepository;
    private $employeeRepository;
    private $employeeMawqifRepository;
    private $leaveRepository;
    private $leaveTypeRepository;
    private $holidayRepository;
    private $employeeStatusPeriodRepository;

    public function __construct(AttendanceRepository $attendanceRepository, LeaveRepository $leaveRepository, LeaveTypeRepository $leaveTypeRepository, HolidayRepository $holidayRepository, EmployeeRepository $employeeRepository, DepartmentRepository $departmentRepository, EmployeeMawqifRepository $employeeMawqifRepository, EmployeeStatusPeriodRepository $employeeStatusPeriodRepository)
    {
        $this->middleware('auth');
        $this->attendanceRepository = $attendanceRepository;
        $this->employeeRepository = $employeeRepository;
        $this->departmentRepository = $departmentRepository;
        $this->employeeMawqifRepository = $employeeMawqifRepository;
        $this->leaveRepository = $leaveRepository;
        $this->leaveTypeRepository = $leaveTypeRepository;
        $this->holidayRepository = $holidayRepository;
        $this->employeeStatusPeriodRepository = $employeeStatusPeriodRepository;
    }

    /**
     * Get employees [کارمندان]
     * This function returns only the requested amount of records [10, 25, 50...]
     * @param request contains perPage param
     */
    public function getEmployees(Request $request)
    {


        try {
            if (canDo('attendance-employees_list')) {
                // per page
                $perPage = $request->input('perPage', 10);

                // get data from repository

                $data = $this->attendanceRepository->getEmployees($perPage);

                // parent departments
                // $parent_departments = $this->departmentRepository->getDepartmentsRendered('parent', null, 1, false);
                $parent_departments = $this->departmentRepository->getDepartmentsRendered('parent', null, 1, false, false);

                // employee statuses
                $employee_statuses = $this->employeeRepository->getEmployeeStatuses();

                // return view with data
                return view('pages.attendance.attendance.employees', ['data' => $data, 'parent_departments' => $parent_departments, 'employee_statuses' => $employee_statuses, 'pager' => withPaginate(['searchRouteName' => 'attendance.employees.search', 'jsCallBack' => 'setTotals()'])]);
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            // dd($ex);
            Log::error($ex->getMessage());
            return redirect()->back()->with('error', __('general.something_wrong_happened'));

            //throw $ex;
        }
    }

    /**
     * Search employees [جستجوی کارمندان]
     * This function returns only the requested amount of records [10, 25, 50...]
     * @param request contains perPage param
     */
    public function searchEmployees(Request $request)
    {
        try {

            // get data from repository
            $data = $this->attendanceRepository->searchEmployees($request);

            // return view with data
            return view('components.pages.attendance.attendance.datatable', ['data' => $data]);
        } catch (\Exception $ex) {
            return $this->getErrorResponse(null, trans('recruitment/recruitment.search_employees_failed'), $ex);
        }
    }

    /**
     * Get continues absent employees [کارمندان که دارای رخصتی متراتر است]
     * This function returns only the requested amount of records [10, 25, 50...]
     * @param request contains perPage param
     */
    public function getContinuesAbsentEmployees(Request $request)
    {
        try {
            if (canDo('attendance-show_frequent_absentees')) {
                // per page
                $perPage = $request->input('perPage', 10);

                // get data from repository
                $data = $this->attendanceRepository->getContinuesAbsentEmployees($perPage);

                // parent departments
                $parent_departments = $this->departmentRepository->getDepartmentsRendered('parent', null, 1, false);

                // employee statuses
                $employee_statuses = $this->employeeRepository->getEmployeeStatuses();

                // return view with data
                return view('pages.attendance.attendance.continues_absent_employees', ['data' => $data, 'parent_departments' => $parent_departments, 'employee_statuses' => $employee_statuses, 'pager' => withPaginate(['searchRouteName' => 'attendance.employees.continues-absent.search', 'jsCallBack' => 'setTotals()'])]);
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return redirect()->back()->with('error', __('general.something_wrong_happened'));

            //throw $ex;
        }
    }

    /**
     * Get continues absent employee details
     * @param request contains askari id param
     */
    public function getContinuesAbsentEmployeeDetails(Request $request, $id)
    {
        try {
            if (canDo('attendance-show_frequent_absentees')) {
                // get the record from repository
                $data['employee'] = $this->employeeRepository->getEmployeeById($id);
                $data['attendance_notify_fire_record'] = AttendanceNotifyFire::where('employee_id', $id)->get()->first();

                if (!is_null($data['employee']) && !is_null($data['attendance_notify_fire_record'])) {
                    $dates = explode(',', $data['attendance_notify_fire_record']->days);

                    $date_string_shamsi = [];
                    foreach ($dates as $date) {
                        array_push($date_string_shamsi, dateTo($date, 'shamsi', false));
                    }
                    $data['days'] = $date_string_shamsi;
                }

                // render the component
                $rendered = View::make('components.pages.attendance.attendance.continues_absent_employees.employee_details_modal', ['data' => $data])->render();

                // send success response
                return $this->getSuccessResponse(['rendered' => $rendered], trans('attendance/att.continues_absent_employee_details_fetched'));
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return $this->getErrorResponse(null, trans('attendance/att.continues_absent_employee_details_fetch_failed'), null, $ex);
            //throw $ex;
        }
    }

    /**
     * Search continues absent employees [جستجوی کارمندان که دارای رخصتی متراتر است]
     * This function returns only the requested amount of records [10, 25, 50...]
     * @param request contains perPage param
     */
    public function searchContinuesAbsentEmployees(Request $request)
    {
        try {
            if (canDo('attendance-show_frequent_absentees')) {
                // get data from repository
                $data = $this->attendanceRepository->searchContinuesAbsentEmployees($request);

                // return view with data
                return view('components.pages.attendance.attendance.continues_absent_employees.datatable', ['data' => $data]);
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return $this->getErrorResponse(null, trans('recruitment/recruitment.search_employees_failed'), $ex);

            //throw $ex;
        }
    }

    /**
     * Search continues absent employees and insert them in [attendance_notify_fire] table
     * This function returns only the requested amount of records [10, 25, 50...]
     * This function is written by [Moh hanif formoly] in old HRMIS [FilterEmpAttendance()][2022-01-20]
     * @param request contains perPage param
     */
    public function checkContinuesAbsentEmployees(Request $request)
    {
        try {
            if (canDo('documents-emp_view_promotions_info')) {
                FacadesDB::beginTransaction();
                $Dates = getEployeeMonthsForAttendanceNotification();
                $DaysOfNotification = getDaysOfTheMonth($Dates['fromDate'], $Dates['toDate']);
                $AllEmplyeessWithTowentyDaysAbsentRecord = [];

                $filter = [];

                if (is_null($request->parent_department) || $request->parent_department == '' || $request->parent_department == 0) {
                    $filter =
                        [
                            't1.rfid != "null" '
                                . ' AND dep.status = 1'
                                . ' AND t1.current_status = 1'
                                . ' AND t1.employee_type_id ' . ($request->check_employee_type == 'all' ? ' != 0 ' : (' = ' . $request->check_employee_type))

                        ];
                } else if (!is_null($request->parent_department) && $request->parent_department != '' && $request->parent_department != 0 && (is_null($request->department) || $request->department == '' || $request->department == 0)) {
                    $filter =
                        [
                            't1.rfid != "null" '
                                . ' AND dep.status = 1'
                                . ' AND t1.current_status = 1'
                                . ' AND t1.employee_type_id ' . ($request->check_employee_type == 'all' ? ' != 0 ' : (' = ' . $request->check_employee_type))
                                . ' AND t1.parent_department_id = ' . decrypt($request->parent_department)
                        ];
                } else if (!is_null($request->parent_department) && $request->parent_department != '' && $request->parent_department != 0 && !is_null($request->department) && $request->department != '' && $request->department != 0) {
                    $filter =
                        [
                            't1.rfid != "null" '
                                . ' AND dep.status = 1'
                                . ' AND t1.current_status = 1'
                                . ' AND t1.employee_type_id ' . ($request->check_employee_type == 'all' ? ' != 0 ' : (' = ' . $request->check_employee_type))
                                . ' AND t1.department_id = ' . decrypt($request->department)
                        ];
                }

                $employees = FacadesDB::table('employees AS t1')->select(
                    't1.id',
                    't1.rfid'
                )
                    ->whereRaw($filter[0])
                    ->join('departments AS dep', 'dep.id', '=', 't1.department_id')
                    ->orderBy('t1.id', 'asc')
                    ->get();


                foreach ($employees as $emp) {
                    $AllEmpPresentDays = $this->attendanceRepository->getEmployeeAttendanceDates($Dates['fromDate'], $Dates['toDate'], $emp->rfid);
                    $AllEmpPresentDaysCount = count($AllEmpPresentDays);
                    $EmplyeeAbsentOrNotAbsentStatus = $this->attendanceRepository->EmplyeeAbsentOrNotAbsent($AllEmpPresentDaysCount, $DaysOfNotification['count']);
                    if ($EmplyeeAbsentOrNotAbsentStatus[0][0] == true) {
                        $AllAbsentDays = array_diff($DaysOfNotification['days'], $AllEmpPresentDays);

                        $AllAbsentDays = array_values($AllAbsentDays);

                        $getEmployeeLeaveDays = [];

                        foreach ($DaysOfNotification['days'] as $dy) {
                            if ($this->leaveRepository->checkOneDayLeave($emp->id, $dy)) {
                                array_push($getEmployeeLeaveDays, $dy);
                            }
                        }

                        $EmployeeLeaveDaysCount = count($getEmployeeLeaveDays);

                        if (($DaysOfNotification['count'] - ($EmployeeLeaveDaysCount + $AllEmpPresentDaysCount)) > 16) {


                            // if an employee Absent days get minues from its count of leaves and its still equal and greather to 17 so 
                            // we need to go insite days of the leave dates and search if it has multiple leaves or not. 
                            // and if its not greather thatn 17 so no need to check rest of process employee has at leased one day leave 


                            $AllHolidyasInTheSpecifiedDates = $this->holidayRepository->getHolidayBetweenDates($Dates['fromDate'], $Dates['toDate']);
                            $temp = [];
                            foreach ($AllHolidyasInTheSpecifiedDates as $dy) {
                                array_push($temp, $dy->date);
                            }
                            $AllHolidyasInTheSpecifiedDates = $temp;
                            $temp = [];
                            // here we merge employee holidays with emplyee leaves in order to calculate total off days 
                            // to not count it as attendance day

                            $totalOffDays = array_merge($getEmployeeLeaveDays, $AllHolidyasInTheSpecifiedDates);

                            //then to have all unique days of array we need get unique dates only 
                            $totalOffDays = array_unique($totalOffDays);

                            // then in order to re index the array keys we need to do
                            $totalOffDays = array_values($totalOffDays);

                            // if total of emplyee Absent days minuse total of  off days + emplyee present days and the employee still has a value of grater or equal to 17 so this emplye 
                            // should possibly have a 20 days continues Absent days , else would no .
                            if (($DaysOfNotification['count'] - (count($totalOffDays) + $AllEmpPresentDaysCount)) > 16) {
                                // we need to now check the 20 days continues days 
                                $allEmployeeDaysCountedAsPresent = array_values(array_unique(array_merge($totalOffDays, $AllEmpPresentDays)));
                                $allEmployeeDaysCountedAsAbsent = array_values(array_diff($DaysOfNotification['days'], $allEmployeeDaysCountedAsPresent));

                                $generateContinuesDatesFromAbsentStartAndEnddate = getDaysOfTheMonth($allEmployeeDaysCountedAsAbsent[0], date('Y-m-d', strtotime($allEmployeeDaysCountedAsAbsent[count($allEmployeeDaysCountedAsAbsent) - 1] . "+1 days")));

                                $countTotalContinuesDays = $generateContinuesDatesFromAbsentStartAndEnddate['count'];
                                $DayContinuesCounter = 0;
                                $ContinuesAbsentDays = [];


                                for ($i = 0; $i < $countTotalContinuesDays; $i++) {
                                    if ((array_search($generateContinuesDatesFromAbsentStartAndEnddate['days'][$i], $allEmployeeDaysCountedAsAbsent)) !== false) {


                                        array_push($ContinuesAbsentDays, $generateContinuesDatesFromAbsentStartAndEnddate['days'][$i]);
                                        $DayContinuesCounter++;
                                        //unset($allEmployeeDaysCountedAsAbsent[$key]);

                                        if ($DayContinuesCounter > 19) {
                                            array_push($AllEmplyeessWithTowentyDaysAbsentRecord, ['record' => $ContinuesAbsentDays, 'emp_id' => $emp->id]);
                                            $i = $countTotalContinuesDays;
                                            $DayContinuesCounter = 0;
                                            $ContinuesAbsentDays = [];
                                        }
                                    } else {
                                        if ($DayContinuesCounter > 19) {

                                            array_push($AllEmplyeessWithTowentyDaysAbsentRecord, ['record' => $ContinuesAbsentDays, 'emp_id' => $emp->id]);
                                            $i = $countTotalContinuesDays;

                                            $DayContinuesCounter = 0;
                                            $ContinuesAbsentDays = [];
                                        } else {
                                            if ($countTotalContinuesDays - $i < 18) {
                                                $i = $countTotalContinuesDays;
                                                $DayContinuesCounter = 0;
                                                $ContinuesAbsentDays = [];
                                            } else {
                                                $DayContinuesCounter = 0;
                                                $ContinuesAbsentDays = [];
                                            }
                                        }
                                    }
                                }
                                //	return response()->json(['generateContinuesDatesFromAbsentStartAndEnddate' => $generateContinuesDatesFromAbsentStartAndEnddate, 'allEmployeeDaysCountedAsAbsent' => $allEmployeeDaysCountedAsAbsent, 'allEmployeeDaysCountedAsPresent' => $allEmployeeDaysCountedAsPresent, 'DaysOfNotification' => $DaysOfNotification['days'],'countAbsent'=> count($allEmployeeDaysCountedAsAbsent), 'continuesDays' => $ContinuesAbsentDays]);

                            }
                        }
                    }
                }

                $array = ['data' => $AllEmplyeessWithTowentyDaysAbsentRecord, 'date_from' => $Dates['fromDate'], 'date_to' => $Dates['toDate']];
                $status = ['status' => $this->attendanceRepository->InsertAtt_notify_fire_Data($array), 'selected_dep' => $request->department];
                FacadesDB::commit();
                return $this->getSuccessResponse($status, trans('attendance/att.continues_absent_employees_checkup_finished'));
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            FacadesDB::rollback();
            return redirect()->back()->with('error', __('general.continues_absent_employees_checkup_failed'));
            //throw $ex;
        }
    }

    public function updateContinuesAbsentEmployeeMark(Request $request)
    {
        try {
            if (canDo('attendance-create_report_of_frequent_absentees')) {
                // get record
                $record = AttendanceNotifyFire::where('id', $request->attendance_notify_fire_id)->get()->first();

                // check if record is available
                if (!is_null($record)) {
                    // update record
                    $record->mark_read = $record->mark_read == 0 ? 1 : 0;
                    $record->remarks = $request->remarks;
                    $record->updated_by = auth()->user()->id;
                    $record->save();
                    return $this->getSuccessResponse(null, trans('general.updated_success'));
                }

                // failed to update
                return $this->getErrorResponse(null, trans('attendance/att.operation_failed'));
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return $this->getErrorResponse(null, trans('attendance/att.operation_failed'), $ex);

            //throw $ex;
        }
    }

    /**
     * One employee attendance [حاضری کارمند]
     * @param request contains perPage param
     */
    public function showEmployeeAttendace(Request $request, $year, $month, $employee_id, $tab)
    {

        try {
            dd($request->end_date);
            
            if (canDo('check_attendance')) {
                $id = decrypt($employee_id);

                $data['year'] = decrypter($year);
                $data['month'] = decrypter($month, false, false, true);

                $data['month'] = 6;
                // $data['month'] = decrypter($month, false, false, true);
                // $data['month'] = 6;
                $data['month'] = decrypter($month, false, false, true);
          

                $data['tab'] = $tab;

                // employee
                $data['employee'] = $this->employeeRepository->getEmployeeById($id);

                // check if employee is available
                if (is_null($data['employee'])) {
                    abort(404);
                }
                $data['leaves'] = [];
                $data['leave_types'] = [];
                $data['leaves_totals'] = [];
                if ($tab == 'images') { // images
                    // month start date
                    $start_date = dateTo((att_month_days(0) . '-' . ((int) $data['month']) . '-' . $data['year']), 'miladi', false);
                    $end_date = dateTo(getLastDayOfShamsiMonth($data['year'], $data['month']) . '-' . $data['month'] . '-' . $data['year'], 'miladi', false);

                    //the status of period 
                    $data['statusPeriods'] = $this->employeeStatusPeriodRepository->getEmployeeStatusDatesByType($id, [$data['year'], $data['month']], [EmployeeStatus::$FIRED, EmployeeStatus::$RESIGN, EmployeeStatus::$TANQIS]);

                    $data['holidays'] = $this->holidayRepository->getHolidayBetweenDates($start_date, $end_date);
                    $data['leaves'] = $this->leaveRepository->getLeavesBetweenDates($start_date, $end_date, $id);

                    $data['images'] = $this->attendanceRepository->getEmployeeImages($id, $data['year'],  $data['month']);
                } else if ($tab == 'leaves') { // leaves

                    // $data['leaves'] = $this->leaveRepository->getLeaves($id, $data['year'], $this->leaveTypeRepository->userLeaveTypes('normalLeave'));
                    $data['leaves'] = $this->leaveRepository->getLeaves($id, $data['year'], config('lists.attendance.leaveTypesCategory')[1], null, true);

                    $data['leaves_totals'] = $this->leaveRepository->getLeavesTotal($data['leaves'], $id, $data['year']);
                    $data['leave_types'] = $this->leaveTypeRepository->getAllLeaveTypes(config('lists.attendance.leaveTypesCategory')[1]);
                } else if ($tab == 'other_issues') { // other_issues

                    // $data['leaves'] = $this->leaveRepository->getLeaves($id, $data['year'], $this->leaveTypeRepository->userLeaveTypes('otherIssue'));
                    $data['leaves'] = $this->leaveRepository->getLeaves($id, $data['year'], config('lists.attendance.leaveTypesCategory')[2], null, true);

                    $data['leaves_totals'] = $this->leaveRepository->getLeavesTotal($data['leaves'], $id, getCurrentShamsiYear());
                    $data['leave_types'] = $this->leaveTypeRepository->getAllLeaveTypes(config('lists.attendance.leaveTypesCategory')[2]);
                } else if ($tab == 'leaves_transfer') { // leave_transfer
                    $data['leaves_transfer'] = $this->leaveRepository->getExtraEntertainmentLeaves(decrypt($request->employee_id));
                } else if ($tab == 'employee_taken_leaves') {
                    $data['employee_taken_leaves'] = $this->leaveRepository->getEmployeeTakenLeaves($id, getCurrentShamsiYear());
                    $data['leaves'] = $this->leaveRepository->getLeaves($id, $data['year']);
                    $data['leaves_totals'] = $this->leaveRepository->getLeavesTotal($data['leaves'], $id, getCurrentShamsiYear());
                }

                $data['employee_mawqif'] = $this->employeeMawqifRepository->getEmployeeMawqifById($data['employee']->employee_mawqif_id);
                $data['current_status'] = $this->employeeRepository->getEmployeeStatusById($data['employee']->current_status);

                return view('pages.attendance.attendance.employee_attendance', ['data' => $data]);
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            // dd(config('lists.attendance.leaveTypesCategory')[1]);
            log::error($ex->getMessage());

            return redirect()->back()->with('error', __('general.something_wrong_happened') . $ex->getMessage());
            //throw $ex;
        }
    }

    /**
     * One employee attendance [حاضری کارمند بعدی]
     * @param request contains perPage param
     */
    public function showNextEmployeeAttendace(Request $request, $year, $month, $employee_id)
    {
        try {
            if (canDo('attendance_review_photos')) {
                // get employee
                $id = decrypt($employee_id);
                $employee = $this->employeeRepository->getEmployeeById($id);
                if (is_null($employee)) {
                    abort(404);
                }

                $sibling_employee = $this->employeeRepository->getSiblingEmployeeByTainatNumber($employee->department_id, $employee->tainat_number);

                if (!is_null($sibling_employee)) {
                    return redirect()->route('attendance.employee.attendance', ['year' => $year, 'month' => $month, 'tab' => 'images', 'employee_id' => encrypt($sibling_employee->id)]);
                }

                return redirect()->route('attendance.employee.attendance', ['year' => $year, 'month' => $month, 'tab' => 'images', 'employee_id' => encrypt($employee->id)]);
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return redirect()->back()->with('error', __('general.something_wrong_happened'));

            //throw $ex;
        }
    }

    /**
     * One employee attendance [حاضری کارمند قبلی]
     * @param request contains perPage param
     */
    public function showPreviousEmployeeAttendace(Request $request, $year, $month, $employee_id)
    {
        try {
            if (canDo('documents-emp_view_promotions_info')) {
                $id = decrypt($employee_id);
                $employee = $this->employeeRepository->getEmployeeById($id);
                if (is_null($employee)) {
                    abort(404);
                }

                $previous_employee = $this->employeeRepository->getPreviousEmployeeByTainatNumber($employee->department_id, $employee->tainat_number);

                if (!is_null($previous_employee)) {

                    return redirect()->route('attendance.employee.attendance', ['year' => $year, 'month' => $month, 'tab' => 'images', 'employee_id' => encrypt($previous_employee->id)]);
                }

                return redirect()->route('attendance.employee.attendance', ['year' => $year, 'month' => $month, 'tab' => 'images', 'employee_id' => encrypt($employee->id)]);
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return redirect()->back()->with('error', __('general.something_wrong_happened'));

            //throw $ex;
        }
    }

    /**
     * One employee attendance [حاضری کارمند]
     * @param request contains perPage param
     */
    public function showMyAttendace(Request $request, $year, $month)
    {

        try {
            if (canDo('check_attendance')) {
                $id = auth()->user()->employee_id;
                $data['year'] = $year = decrypter($year);
                $data['month'] = $month = decrypter($month);
                // employee
                $data['employee'] = $this->employeeRepository->getEmployeeById($id);

                // check if employee is available
                if (is_null($data['employee'])) {
                    abort(404);
                }

                // month start date
                $start_date = dateTo((att_month_days(0) . '-' . ((int) $data['month']) . '-' . $data['year']), 'miladi', false);
                $end_date = dateTo(getLastDayOfShamsiMonth($data['year'], $data['month']) . '-' . $data['month'] . '-' . $data['year'], 'miladi', false);

                $data['holidays'] = $this->holidayRepository->getHolidayBetweenDates($start_date, $end_date);
                $data['leaves'] = $this->leaveRepository->getLeavesBetweenDates($start_date, $end_date, $id);

                $data['images'] = $this->attendanceRepository->getEmployeeImages($id, $year, $month);

                // return view with data
                return view('pages.attendance.attendance.my_attendance', ['data' => $data, 'employee' => $data['employee']]);
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return redirect()->back()->with('error', __('general.something_wrong_happened'));

            //throw $ex;
        }
    }

    /**
     * Reject image
     * The request is validated in use LeaveDeleteRequest
     * @param request contains image_id
     */
    public function rejectImage(ImageRejectionRequest $request)
    {

        try {
            if (canDo('attendance-remove_emp_att_img')) {
                // reject image
                $rejected = $this->attendanceRepository->rejectImage($request->image_id);

                // rejected successfully image
                if ($rejected != false) {
                    return $this->getSuccessResponse(null, trans('attendance/att.image_rejected'));
                }
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return $this->getErrorResponse(null, trans('attendance/att.image_reject_failed'), $ex);
            //throw $ex;
        }
    }


    public function getAttendanceInOutCount()
    {
        try {
            $data['in'] = $this->employeeRepository->getAttendanceInEmployeesCount();
            $data['out'] = $this->employeeRepository->getAttendanceOutEmployeesCount();
            return $this->getSuccessResponse(['data' => $data], trans('general.fetched_success'));
        } catch (\Exception $ex) {
            throw $ex;
        }
    }

    public function searchContinuesAbsentEmployeesForExcel() {}
}
