<?php

return [

    'my_attendance' => 'حاضری من',
    'attendance' => 'حاضری',
    'machines' => 'ماشین های حاضری',
    'machineList' => 'لست ماشین ها',
    'machinemanagement' => 'مدیریت ماشین ها',
    'ip' => 'آی پی',
    'location' => 'موقعیت',
    'name' => 'نام',
    'active' => 'فعال',
    'deactive' => ' غیر فعال',
    'checkmachines' => 'بررسی ماشین ها',
    'skip_chech' => 'بدون بررسی',
    'branch' => 'شاخه',
    'deactive_machines' => 'ماشین های غیر فعال',
    're_store_img' => 'ذخیره مجدد',
    're_store_info' => 'داده های قبلی ماشین های انتخاب شده حذف و دوباره ثبت خواهد گردید',
    'machine_allready_inserted' => 'ماشین های ذیل قبلا آپلود شده است',
    'uploads_att_progress' => 'آپلود در حال جریان قرار دارد',
    'machine_has_no_data' => 'ماشین های انتخاب شده در این تاریخ دیتا ندارند',
    'emp_attendance' => 'حاضری کارمندان',
    'check_images' => 'بررسی عکسها ',
    'leave_mgmt' => 'مدیریت رخصتی ',
    'station' => 'ستیشن',
    'stations' => 'ستیشن ها',
    'noOfMachines' => 'تعداد ماشین ها',
    'stationLimitInofText' => 'در هر ستیشن از یک الی 24 ماشین فعال گنجایش ثبت را دارد',
    'macAddress' => 'مک آدرس',
    'setAttStationsPermissions' => 'تعین صلاحیت شتیشن های حاضری',
    'stationAccess' => 'دست رسی به ستیشن های حاضری',
    'denyDepAccess' => 'رد دسترسی به ادارات',
    'cant_store_duplicate_machine_mac_at_same_branch' => 'نمیتوان ماشین حاضری با مک آدرس تکراری را در برانچ واحد ثبت کرد',
    'AccessToAllDepartments' => 'دسترسی به تمامی ادارات',
    'holiday_record_created' => 'رخصتی ایجاد گردید',
    'holiday_record_create_failed' => 'رخصتی ایجاد نگردید',
    'holiday_already_exists' => 'تاریخ (:date) از قبل رخصتی میباشد',
    'holiday_fetched' => 'رخصتی دریافت شد',
    'holiday_fetch_failed' => 'رخصتی دریافت نشد',
    'holiday_record_updated' => 'رخصتی تصحیح شد',
    'holiday_record_update_failed' => 'رخصتی تصحیح نشد',
    'holiday_record_deleted' => 'رخصتی حذف شد',
    'holiday_record_delete_failed' => 'رخصتی حذف نشد',
    'holidays_export_to_excel_file_title' => 'راپور رخصتی ها (:date)',
    'attendance_department' => 'آمریت حاضری',
    'holidays_excel_report_generation_failed' => 'ایجاد راپور رخصتی ها انجام نشد',
    'search_holidays_failed' => 'جصتجوی رخصتی ها انجام نشد',
    'announcement_record_created' => 'اطلاعیه ایجاد گردید',
    'announcement_record_create_failed' => 'اطلاعیه ایجاد نگردید',
    'announcement_fetched' => 'اطلاعیه دریافت شد',
    'announcement_fetch_failed' => 'اطلاعیه دریافت نشد',
    'announcement_record_updated' => 'اطلاعیه تصحیح شد',
    'announcement_record_update_failed' => 'اطلاعیه تصحیح نشد',
    'announcement_record_deleted' => 'اطلاعیه حذف شد',
    'announcement_record_delete_failed' => 'اطلاعیه حذف نشد',
    'announcements_export_to_excel_file_title' => 'راپور اطلاعیه ها (:date)',
    'announcements_excel_report_generation_failed' => 'ایجاد راپور اطلاعیه ها انجام نشد',
    'search_announcements_failed' => 'جصتجوی اطلاعیه ها انجام نشد',
    'leave_request_at_firday' => 'تاریخ (:date) جمعه میباشد',
    'leave_request_at_holiday' => 'تاریخ (:date) رخصتی عمومی میباشد',
    'mutiple_leaves_at_same_date' => 'کارمند ذیل در تاریخ (:date) دارای رخصتی میباشد',
    'start_date_is_friday' => 'تاریخ آغاز رخصتی (:date) جمعه میباشد',
    'end_date_is_friday' => 'تاریخ ختم رخصتی (:date) جمعه میباشد',
    'a_day_between_the_selected_days_is_already_leave' => 'در بین تاریخ های انتخاب شده، یک تاریخ آن از قبل رخصتی کارمند میباشد',
    'a_day_between_the_selected_days_employee_is_present' => 'کارمند مذکور در یکی از تاریخ های درج شده حاضر می باشد',
    'necessary_leaves_exceed' => 'از رخصتی ضروری کارمند فقط (:count) روز باقی مانده است',
    'entertainment_leaves_exceed' => 'از رخصتی تفریحی کارمند فقط (:count) روز باقی مانده است',
    'sick_leaves_exceed' => 'از رخصتی مریضی کارمند فقط (:count) روز باقی مانده است',
    'hajj_leaves_exceed' => 'از رخصتی حج کارمند فقط (:count) روز باقی مانده است',
    'employee_is_not_eligible_yet_for_entertainment_leave' => 'کارمند مستحق رخصتی تفریحی نمیباشد',
    'necessary_leave_is_only_10_days' => 'کارمند مستحق ۱۰ روز رخصتی ضروری میباشد',
    'entertainment_leave_is_only_20_days' => 'کارمند مستحق ۲۰ روز رخصتی تفریحی میباشد',
    'sick_leave_is_only_20_days' => 'کارمند مستحق ۲۰ روز رخصتی مریضی میباشد',
    'request_sick_leave_is_only_5_days' => 'کارمند مستحق درخواست ۵ روز رخصتی مریضی میباشد',
    'hajj_leave_is_only_45_days' => 'کارمند مستحق ۴۵ روز رخصتی حج میباشد',
    'other_issue_leave_recurrent_days' => 'از تاریخ آغاز الی ختم فقط به روز های انتخاب شده رخصتی وضع میشود.',
    'leave_record_created' => 'رخصتی درچ گردید',
    'employee_is_not_eligible_for_extra_entertainment_leave' => 'کارمند مذکور برای رخصتی اضافه تفریحی واجد شرایط نمیباشد',
    'leave_not_found' => 'رخصتی دریافت نشد',
    'extra_entertainment_leave_is_only_20_days' => 'کارمند مستحق ۲۰ روز رخصتی اضافه تفریحی میباشد',
    'extra_entertainment_leaves_exceed' => 'از اضافه رخصتی تفریحی کارمند فقط (:count) روز باقی مانده است',
    'leave_record_updated' => 'رخصتی تصحیح شد',
    'are_you_sure_to_delete_leave' => 'آیا مطمئن هستید که رخصتی را حذف نماید؟',
    'incorrect_years_selected' => 'ترتیب سال ها درست انتخاب نشده است',
    'extra_entertainment_leaves_transfered' => 'رخصتی های کارمند مذکور انتقال گردید',
    'entertainment_leaves_failed_to_transfer_because_employee_has_used_his_leaves' => 'کارمند مذکور در سال گذشته حد اقل یک روز از رخصتی های تفریحی خود را اخذ نموده است',
    'entertainment_leaves_already_transfered' => 'رخصتی تفریحی کارمند مذکور از قبل انتقال شده است',
    'continues_absent_employee_marked_unread' => 'کارمند مذکور از لست ملاحظه شده ها حذف گردید',
    'continues_absent_employee_marked_read' => 'کارمند مذکور ملاحظه شده گردید',
    'image_rejected' => 'تصویر کارمند رد گردید',
    'leave_request_record_deleted' => 'درخواست رخصتی شما موفقانه حذف ګردید',
    'leave_request_record_delete_failed' => 'حذف کردن درخواست رخصتی شما ناکام شد',
    'image_reject_failed' => 'تصویر کارمند رد نگردید',
    'employees_annual_leaves_export_to_excel_file_title' => 'راپور سالانه رخصتی ها (:date)',
    'employees_monthly_attendance_export_to_excel_file_title' => 'راپور ماهوار حاضری کارمندان بابت برج :month سال :year (:date)',
    'employees_choise_date_attendance_export_to_excel_file_title' => 'راپور حاضری کارمندان از تاریخ :start_date الی تاریخ :end_date (:date)',
    'employees_daily_attendance_export_to_excel_file_title' => 'راپور روزانه حاضری کارمندان تاریخ (:date)',
    'employees_special_daily_attendance_export_to_excel_file_title' => 'راپور خاص حاضری روزانه کارمندان تاریخ (:date)',
    'employees_daily_attendance_export_to_excel_file_title' => 'راپور روزانه حاضری کارمندان تاریخ (:date)',
    'employees_monthly_time_based_attendance_export_to_excel_file_title' => 'راپور ماهوار حاضری کارمندان به اساس وقت بابت برج :month سال :year (:date)',
    'employees_choise_date_time_based_attendance_export_to_excel_file_title' => 'راپور حاضری کارمندان به اساس وقت از تاریخ :start_date الی تاریخ :end_date (:date)',
    'present_days_count' => 'تعداد روزهای حاضری: :days',
    'e_attendance_employees_export_to_excel_file_title' => 'راپور کارمندان شامل حاضری الکترونیک تاریخ (:date)',
    'leaves_export_to_excel_file_title' => 'راپور رخصتی های :employee (:date)',
    'deactive_machines_found' => 'ماشین های غیر فعال دریافت گردید',
    'student_export_to_excel_file_title' => 'راپور محصلین  (:date)',
    'one_day_leave_only' => 'شما تنها می‌توانید برای یک روز رخصتی اخذ نمایید. لطفاً تاریخ رخصتی را مجدداً تنظیم نمایید.',
    'leave_request_valid_date' => 'تاریخ معتبر درخواست رخصتی',
    'update_leave_request_valid_date' => 'تصحیح تاریخ معتبر درخواست رخصتی',
    'setting_type' => 'نوع تنظیمات', 
    'leave_request_before_valid_date' => 'تاریخ درخواست رخصتی قبل از تاریخ معتبر میباشد (:date)',
    'taken_leaves_days_no' => 'تعداد روزهای رخصتی ګرفته شده',
    'add_taken_leaves' => 'اضافه کردن رخصتی اداره قبلی',
    'request_date_is_more_than_50_days' => 'روز های انتخاب شده از ۵۰ روز زیاد است',
];
