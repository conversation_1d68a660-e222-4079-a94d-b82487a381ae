{{-- 
    DESCRIPTION: LIST OF EMPLOYEES [کارمندان 
--}}
<x-layouts.master title="{{ __('general.employees') }}">
    @pushOnce('content')
        <x-pages.pagesContainer>

            {{-- LINKS --}}
            <div class="pb-2">
                <x-pages.attendance.links />
            </div>

            {{-- START CARD --}}
            <div class="card">
                {{-- START CARD HEADER --}}
                <div class="card-header border-bottom">
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex justify-content-start align-items-center">

                                    <span id="page_title"class="font-size-18-bold">
                                        {{ __('general.total_employees') }}
                                    </span>
                                    {{-- SPINNER --}}
                                    <div class="spinner-border spinner-border-sm text-primary mx-2 d-none"
                                        id="fetching_spinner" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-center align-items-center">
                                    {{-- SIDEBAR TOGGLE --}}
                                    <button id="show_hide_sidebar_btn" class="btn btn-sm btn-secondary mx-1" type="button">
                                        <span class="tf-icons bx bx-sidebar"></span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- START TOTALS --}}
                    <div class="row mt-3">
                        <div class="col-12 d-flex">
                            <x-pages.attendance.attendance.employee_totals :data="$data" />
                        </div>
                    </div>
                    {{-- END TOTALS --}}

                </div>
                {{-- START CARD BODY --}}
                <div class="card-body" style="padding-{{ app()->getLocale() == 'en' ? 'left' : 'right' }}: 0 !important;">
                    <div class="row">
                        {{-- MAIN CONTENT --}}
                        <div class="col-10" id="main_content"
                            style="padding-{{ app()->getLocale() == 'en' ? 'right' : 'left' }}: 0 !important;">
                            <div class="d-flex justify-content-between align-items-center"
                                style="background: #eeeeee7a; border-bottom: 1px solid #c9c9c982;">
                                <div class="row py-2 mx-0" style="flex-grow:1;">
                                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                        <select name="parent_department" id="parent_department"
                                            class="form-select select2 pager-search-select2 pager-search"
                                            onchange="getSubDepartments(this.value, 'department')">
                                            <option value="0">{{ __('general.parent_department') }}</option>
                                            @foreach ($parent_departments as $dept)
                                                <option value="{{ encrypt($dept->id) }}">
                                                    {{ $dept->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>

                                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                        <select name="department" id="department"
                                            class="form-select select2 pager-search-select2 pager-search">
                                            <option value="0">{{ __('general.department') }}</option>
                                        </select>
                                    </div>

                                    {{-- MONTH --}}
                                        {{-- @php
                                            $month = getCurrentShamsiMonth();
                                            $months = Config::get('custom.shamsi.months.' . app()->getLocale());
                                        @endphp
                                        <select name="month" id="month" class="form-select pager-search">
                                            @for ($i = 1; $i <= count($months); $i++)
                                                <option value="{{ encrypt($i) }}" {{ $i == $month ? 'selected' : '' }}>
                                                    {{ $months[$i] }}
                                                </option>
                                            @endfor
                                        </select> --}}
                                        {{-- START DATE --}}
                                        <div class="col-12 col-sm-12 col-md-2 col-lg-2 start_date">
                                            {{-- <label for="start_date"
                                                class="form-label">{{ __('general.start_date') }}</label> --}}
                                            <x-tools.utilities.datepicker.dariDatePicker name="start_date"
                                                withID="start_date" withSize="3"
                                                withPlaceHolder="{{ __('general.start_date') }}"
                                                extraClasses="pager-search" />
                                       
                                        </div>

                                        {{-- END DATE --}}
                                        <div class="col-12 col-sm-12 col-md-2 col-lg-2 end_date">
                                            {{-- <label for="end_date" class="form-label">{{ __('general.end_date') }}</label> --}}
                                            <x-tools.utilities.datepicker.dariDatePicker name="end_date" withID="end_date"
                                                withSize="3" withPlaceHolder="{{ __('general.end_date') }}" 
                                                extraClasses="pager-search"/>

                                        </div>

                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <div id="pager">
                                        <x-pages.attendance.attendance.datatable :data="$data" />
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <div class="d-flex gap-2 my-4">
                                        <div
                                            style="padding-{{ app()->getLocale() == 'en' ? 'left' : 'right' }}: 15px !important;">
                                            {!! $pager !!}

                                        </div>
                                        <div class="">
                                            <x-tools.utilities.datapager.per-page cssClass="mt-1" formSelect="md" />
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>

                        {{-- STRT SIDEBAR --}}
                        <x-pages.attendance.attendance.sidebar :employee_statuses="$employee_statuses" />
                        {{-- END SIDEBAR --}}
                    </div>
                </div>
                {{-- END CARD BODY --}}
            </div>
            {{-- START CARD --}}
        </x-pages.pagesContainer>
    @endPushOnce
    @pushOnce('pscript')
        <script src="{{ asset('assets/js/my/prevent_enter.js') }}"></script>
        <script src="{{ asset('assets/js/my/sidebar_toggler.js') }}"></script>
        <script src="{{ asset('assets/js/my/datatable_padding_toggler.js') }}"></script>

        <script>
            // this function is called on search and paginate
            function setTotals() {
                $("#total").text($("#pager-status").attr('data-stotal'));
            }
        </script>
    @endPushOnce
</x-layouts.master>
