@props([
    'cssClass' => '',
    'searchRouteName' => '',
    'pagerItemsContainerID' => 'pager',
    'ElementID_indexer' => 'p',
    'jsCallBack' => null,
    'removeJsCallBack' => null,
])



<ul class="pagination pagination-square pagination-primary p-1 d-flex flex-wrap" id="pager-pages">

</ul>

<script>
    let searchable = '{{ $searchRouteName }}';
    let length = 0,
        ButtonStartFrom = 1,
        ButtonEnd = 1;
    let spiner =
        `<div class="spinner-border spinner-border-sm text-primary" role="status"><span class="visually-hidden">Loading...</span></div>`;


    let ppt;

    function initSearchFeilds() {
        if (searchable) {
            const ActiveClass = document.querySelectorAll(".pager-search");
            for (let i of ActiveClass) {
              
                i.addEventListener("keyup", function(event) {
                    event.preventDefault();
                    if (event.keyCode === 13) {

                        paginator();
                    }

                });
                if (i.nodeName == 'SELECT' && !(i.classList.value.split(' ').includes('offChangeEvent'))) {

                    if ((i.classList.value.split(' ').includes('pager-search-select2'))) {

                        $(i).select2().on('change', paginator);
                    } else {
                        i.addEventListener("change", function(event) {
                            paginator();
                        });
                    }

                } else if (i.type == 'checkbox' && !(i.classList.value.split(' ').includes('offChangeEvent'))) {
                    i.addEventListener("change", function(event) {
                        paginator();
                    });
                } else if (i.type == 'radio' && !(i.classList.value.split(' ').includes('offChangeEvent'))) {
                    i.addEventListener("change", function(event) {
                        paginator();
                    });
                }else {
    // For datepickers (start_date, end_date), include even empty values
    // For other fields, only include non-zero values
    if (i.name === 'start_date' || i.name === 'end_date') {
        seachObg[[i.name]] = $(i).val();
    } else if ($(i).val() != 0) {
        seachObg[[i.name]] = $(i).val();
    }
}

            }

        }

    }

    document.addEventListener("DOMContentLoaded", function(event) {
        createPageButtons();
        initSearchFeilds();
    });

    function setActiveIndex(ref) {

        const ActiveClass = document.querySelectorAll(".page-item");

        for (let i of ActiveClass) {

            // if (i.classList) {
            {{--  i.classList.remove("active");  --}}
            //}

        }

        //if(document.getElementById(ref) != null)document.getElementById(ref).classList.add('active');
    }

    function getSetAttr(elemID, name, value = '', option = 'get') {

        let elem = option == 'get' ? (document.getElementById(elemID).getAttribute(name)) : (document.getElementById(
            elemID).setAttribute(name, value));

        if (elem) {
            return Number(elem);
        }
    }

    function getPagerStatusObject() {
        return {
            scurrentPage: getSetAttr('pager-status', 'data-scurrentpage'),
            slastPage: getSetAttr('pager-status', 'data-slastpage'),
            stotal: getSetAttr('pager-status', 'data-stotal'),
            sperPage: getSetAttr('pager-status', 'data-sperpage'),
            snumberOfButtons: getSetAttr('pager-status', 'data-numberofbuttons')
        }
    }

    function createPageButtons(isAvailable = true) {
        if (!isAvailable) {

            document.getElementById('pager-pages').innerHTML =
                "<div class='d-flex flex-row mx-4'>{{ __('general.notfound') }}</div>";

            return;
        } else {



            let pagerlinks = '',
                nextpage = '',
                lastpage = '',
                previouspage = '',
                firspage = '',
                endPageIndex = '';
            let sta = getPagerStatusObject();

            //dgdfgdfgfd

            firspage =
                `<li class="page-item first"><button class="page-link page-link-pager" value="1"><i class="tf-icon bx bx-chevrons-left"></i></button></li>`;

            previouspage = `<li class="page-item prev"><button class="page-link page-link-pager" id="previous"value="` +
                (
                    sta.scurrentPage > 1 ? (sta.scurrentPage - 1) : sta.scurrentPage) +
                ` "><i class="tf-icon bx bx-chevron-left"></i></button></li>`;

            nextpage = `<li class="page-item next">
                                        <button class="page-link page-link-pager" id="next" value="` + (sta
                    .scurrentPage <
                    sta.slastPage ? sta.scurrentPage + 1 : sta.scurrentPage) +
                `"><i class="tf-icon bx bx-chevron-right"></i></button></li>`;

            lastpage = `<li class="page-item last">
                                        <button class="page-link page-link-pager" value="` + sta.slastPage +
                `"><i class="tf-icon bx bx-chevrons-right"></i></button></li>`;

            endPageIndex = `<li class="page-item ` + (sta.scurrentPage == sta.slastPage ? 'active' : '') + `">
                                        <button class="page-link page-link-pager" value="` + (sta.slastPage) + `">` +
                sta.slastPage + `</button></li>`;
            //          let counter=(sta.scurrentPage%sta.snumberOfButtons)==0?sta.scurrentPage:(sta.scurrentPage%sta.snumberOfButtons)+sta.scurrentPage;
            ButtonStartFrom = sta.scurrentPage;
            let counter = 1;
            ButtonStartFrom = (sta.scurrentPage >= sta.snumberOfButtons ? (ButtonStartFrom - 1 == 0 ? 1 :
                (sta.snumberOfButtons == 0 ? 1 : ButtonStartFrom - 1)) : (ButtonStartFrom !=
                1 ? (
                    ButtonStartFrom - 1) : ButtonStartFrom));
            let buttonsStarts = sta.snumberOfButtons == 0 ? ButtonStartFrom : (sta
                .snumberOfButtons + ButtonStartFrom > sta.slastPage ? sta.slastPage : sta
                .snumberOfButtons + ButtonStartFrom);

            for (let i = ButtonStartFrom; i <= buttonsStarts; i++) {
                if (counter > buttonsStarts) {
                    ButtonEnd = i - 1;
                    break;
                }

                pagerlinks += ` <li class="page-item ` + (sta.scurrentPage == i ? 'active' : '') +
                    `" id="{{ $ElementID_indexer }}` + i + `"><button class="page-link page-link-pager" value="` + i +
                    `">` + i + `</button></li>`;
                counter++;
            }
            let linkButtons = firspage + previouspage + pagerlinks + nextpage + endPageIndex + lastpage;
            document.getElementById('pager-pages').innerHTML = linkButtons;

            const arrClass = document.querySelectorAll(".page-link-pager");
            for (let i of arrClass) {
                i.addEventListener("click", paginator);
            }
        }
    }

    function paginator() {

        let axParams, isSearchActive = false;
        let searchfields = document.querySelectorAll(".pager-search");
        let sta = getPagerStatusObject();
        let isAnySearchFieldHasFilled = false;

        for (let i of searchfields) {
            if (i.value) {
                isAnySearchFieldHasFilled = true;
            }
        }


        if ("{{ isset($searchRouteName) ? $searchRouteName : '' }}".length > 1 && searchfields.length > 0 &&
            isAnySearchFieldHasFilled) {

            routResource = "{{ isset($searchRouteName) ? route($searchRouteName) : '' }}";
            isSearchActive = true;


        } else {

            routResource = routname;
            isSearchActive = false;
        }

        if (this.nodeName == 'BUTTON') { // we only init page buttons at page buttons click


            if (this.id == 'next') {

                sta.scurrentPage = this.value = sta.scurrentPage < sta.slastPage ? sta.scurrentPage + 1 :
                    sta.scurrentPage;


            } else if (this.id == 'previous') {
                sta.scurrentPage = this.value = sta.scurrentPage > 1 ? (sta.scurrentPage - 1) : sta.scurrentPage;

            } else if (this.value) {
                sta.scurrentPage = this.value;
            } else {
                sta.scurrentPage = 1;
            }
            this.innerHTML = spiner;
        } else {
            sta.scurrentPage = 1; // we start from page first after search events are gets trigering
        }

        if (isSearchActive) {

            // let searchName = document.getElementById("pager-search").name;
            const searchables = document.querySelectorAll(".pager-search")
            let seachObg = {};
            for (let i of searchables) {
                // if (i.nodeName == 'SELECT') {
                //     // if (i.value == 0) {
                //     //     continue;
                //     // }
                // }

                if (i.type == 'checkbox') {
                    if (i.name.includes('[]')) {
                        if (i.checked) {
                            if (seachObg[[i.name]] == null) {
                                seachObg[[i.name]] = [i.value];
                            } else {
                                seachObg[[i.name]].push(i.value);
                            }
                        }
                    } else {
                        seachObg[[i.name]] = i.checked;
                        seachObg[[i.name] + '_value'] = i.value;
                    }
                } else if (i.type == 'radio') {
                    if (i.checked) {
                        seachObg[[i.name]] = $(i).val();
                    }
                } else {
                    if ($(i).val() != 0) {
                        seachObg[[i.name]] = $(i).val();
                    }

                }

            }
            seachObg['page'] = sta.scurrentPage;
            seachObg['perPage'] = sta.sperPage;
            axParams = {
                params: seachObg,
                perPage: sta.sperPage
            };

        } else {
            axParams = {
                params: {
                    page: sta.scurrentPage,
                    perPage: sta.sperPage
                }
            };
        }





        let pageID = '{{ $ElementID_indexer }}' + sta.scurrentPage;


        let PageUrl = routResource + '?page=' + sta.scurrentPage;
        let callBack = "{{ $jsCallBack }}";
        let removeJsCallBack = "{{ $removeJsCallBack }}";
        let spinner = document.getElementById('fetching_spinner')
        if (spinner != null) {
            spinner.classList.remove("d-none")
        }
        axiosObj.get(routResource, axParams)
            .then(function(response) {
                if (spinner != null) {
                    spinner.classList.add("d-none")
                }
                document.getElementById('{{ $pagerItemsContainerID }}').innerHTML = response.data;

                if (callBack) {
                    eval(callBack);
                }

                if (removeJsCallBack) {
                    eval(removeJsCallBack);
                }

                if (getSetAttr('pager-status', 'data-savailable') > 0) {

                    setActiveIndex(pageID);
                    if (document.getElementById(pageID)) document.getElementById(pageID).firstChild.innerHTML = sta
                        .scurrentPage;
                    createPageButtons();

                } else {


                    createPageButtons(false)
                    setActiveIndex(pageID);
                    if (document.getElementById(pageID)) document.getElementById(pageID).firstChild.innerHTML = sta
                        .scurrentPage;
                }


            })
            .catch(function(error) {
                DisplayMessage(error, false);

                document.getElementById(pageID).firstChild.innerHTML = sta.scurrentPage;


            })
            .finally(function() {
                // always executed
                pagerLoading = false;
            });


    }


    // Event handler for the select dropdown change

    if (document.getElementById("per_page")) {
        document.getElementById("per_page").addEventListener("change", loadPagination);
    }else{
        setTimeout(() => {
            if(document.getElementById("per_page")){
                document.getElementById("per_page").addEventListener("change", loadPagination);
            }
           
        }, 300);
    }

    function clearNodeEventsByClassName(className = 'pager-search') {
        const elements = document.getElementsByClassName(className);
        Array.from(elements).forEach(element => {
            const clonedElement = element.cloneNode(true);
            element.parentNode.replaceChild(clonedElement, element);
        });
    }

    function loadPagination() {
        var selectedValue = document.getElementById("per_page").value;

        let sta = getPagerStatusObject();
        if (sta.stotal < document.getElementById("per_page").value) {
            sta.scurrentPage = 1;
        }

        selectedValue = Math.ceil(sta.stotal / selectedValue) >= 1 ? selectedValue : (sta.slastPage);
        sta.sperPage = selectedValue;

        getSetAttr('pager-status', 'data-sperpage', sta.sperPage, 'set');
        paginator();

    }
</script>
