@props(['data' => []])
<x-tools.utilities.datapager.ajax_data_pager_utils :data="$data" />

<table class="table table-cust" id="employees_datatable">
    <thead style="background-color: #fff9e5;">
        <tr>

            <th>{{ __('general.number') }}</th>
            <th>{{ __('general.name') }}</th>
            <th>{{ __('general.father_name') }}</th>
            <th>{{ __('general.department') }}</th>
            <th>{{ __('general.employee_type') }}</th>
            <th>{{ __('general.employee_status') }}</th>

            <th title="{{ __('general.included_in_e_attendance') }}" data-bs-toggle="tooltip" data-bs-offset="0,1"
                data-bs-placement="top" data-bs-custom-class="tooltip-secondary">
                <i class="fa-solid fa-fax fs-5"></i>
            </th>
            <th style="padding-{{ app()->getLocale() == 'en' ? 'left' : 'right' }}: 15px !important;">
                {{ __('general.id') }}
            </th>
            <th>{{ __('general.actions') }}</th>
        </tr>
    </thead>

    <tbody>
        @if (!is_null($data) && count($data) > 0)
            @foreach ($data as $number => $item)
                @php
                    $id = encrypt($item->id);
                @endphp
                <tr id="{{ 'employee-' . $id }}">
                    <td>{{ $number + 1 }}</td>
                    <td>{{ $item->name . ' ' . $item->last_name }}</td>
                    <td>{{ $item->father_name }}</td>
                    <td class="limited_width">{{ $item->department }}</td>
                    <td>{{ $item->is_contract == 1 ? __('general.contractor') : $item->employee_type }}</td>

                    <td>
                        {{ $item->current_status_name }}
                    </td>

                    <td>
                        {{ is_null($item->rfid) && !is_null($item->in_attendance) ? __('general.not') : __('general.yes') }}
                    </td>
                    <td style="padding-{{ app()->getLocale() == 'en' ? 'left' : 'right' }}: 15px !important;">
                        {{ $item->id }}</td>
                    <td>
                        @can('attendance_review_photos')
                            <a href="{{ route('attendance.employee.attendance', ['year' => encrypt(getCurrentShamsiYear()), 'month' => request('month', encrypt(getCurrentShamsiMonth())), 'start_date' => request('start_date'), 'employee_id' => $id, 'tab' => 'images']) }}"
                                target="blank" type="button" title="{{ __('general.check') }}"
                                class="btn btn-icon btn-sm btn-outline-vimeo">
                                <i class="fa fa-image"></i>
                            </a>
                        @endcan

                        @can('attendance_view_emp_leave_management')
                            <a href="{{ route('attendance.employee.attendance', ['year' => encrypt(getCurrentShamsiYear()), 'month' => getCurrentShamsiMonth(), 'employee_id' => $id, 'tab' => 'leaves']) }}"
                                target="blank" type="button" title="{{ __('general.leaves') }}"
                                class="btn btn-icon btn-sm btn-outline-secondary">
                                <i class="fa fa-sign-out"></i>
                            </a>
                        @endcan
                       
                       @if(canDo('attendance-view_employee_leave_request_page') && $item->employee_mawqif_id==1) 
                            <a href="{{ route('executive_leave_request.index', ['id' => $id]) }}"
                                target="blank" type="button" title="{{ __('general.leave_request') }}"
                                class="btn btn-icon btn-sm btn-outline-success">
                                <i class="fa-solid fa-person-walking-arrow-right"></i>
                            </a>
                        @endif

                    </td>
                </tr>
            @endforeach
        @else
            <tr>
                <td colspan="8">
                    <p class="text-center">{{ __('general.not_available') }}</p>
                </td>
            </tr>
        @endif
    </tbody>
</table>
